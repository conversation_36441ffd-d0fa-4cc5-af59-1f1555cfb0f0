# 告警监控系统 - 邮件配置说明

## 📧 QQ邮箱配置步骤

### 1. 开启QQ邮箱SMTP服务
1. 登录QQ邮箱 (https://mail.qq.com)
2. 点击右上角"设置" → "账户"
3. 找到"POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务"
4. 开启"SMTP服务"
5. 按照提示发送短信，获取**授权码**（16位字符）
6. **重要：保存好这个授权码，这就是邮件配置中的密码**

### 2. 在告警监控系统中配置邮件

#### 打开邮件设置
- 启动告警监控系统
- 点击界面上的"📧 邮件设置"按钮

#### 填写配置信息
```
✅ 启用发送告警邮件：勾选

SMTP服务器：smtp.qq.com
端口：465
✅ 使用SSL：勾选

用户名：<EMAIL>
密码：[填入第1步获取的16位授权码]
发件人：<EMAIL>
收件人：<EMAIL>（或其他接收邮箱）
```

#### 测试邮件配置
1. 填写完配置后，点击"🧪 发送测试邮件"
2. 如果配置正确，会显示"测试邮件发送成功"
3. 检查收件箱（包括垃圾邮件文件夹）是否收到测试邮件

### 3. 邮件发送规则

系统会在以下情况自动发送告警邮件：

#### 新告警邮件
- 检测到新的告警时
- 检测到重点关注的告警时
- 检测到关联告警时

#### 持续阈值邮件
- 告警持续1小时、2小时、3小时、4小时、8小时、24小时、48小时、72小时时
- 每个阈值只发送一次，避免重复

### 4. 常见问题

#### Q: 提示"SMTP认证失败"
A: 检查以下几点：
- 确认用户名是完整的QQ邮箱地址
- 确认密码是QQ邮箱的授权码，不是QQ密码
- 确认已开启QQ邮箱的SMTP服务

#### Q: 提示"连接失败"
A: 检查网络连接和防火墙设置：
- 确认能访问smtp.qq.com
- 确认端口465未被防火墙阻止

#### Q: 邮件发送成功但收不到
A: 检查以下位置：
- 收件箱
- 垃圾邮件文件夹
- 已删除邮件文件夹
- 邮件过滤规则

#### Q: 想要修改收件人
A: 
- 点击"📧 邮件设置"
- 修改"收件人"字段
- 点击"保存"
- 建议发送测试邮件验证

### 5. 邮件内容示例

#### 新告警邮件
```
主题：[告警通知] 新/重点/关联(联通) 14:30:25

内容：
告警通知
==================================================
【告警 1】
告警名称：CPU过载
网元名称：BTS_001
IP地址：*************
发生时间：2024-01-15 14:30:00
级别：严重
位置信息：机房A-1层
------------------------------------------
```

#### 持续阈值邮件
```
主题：[告警通知][持续阈值 2h] 组:BTS_001 共1 14:30:25

内容：
持续阈值告警通知
==================================================
阈值：120分钟（2小时）
【告警 1】
告警名称：外部扩展设备故障
网元名称：BTS_001
持续时间：125分钟
------------------------------------------
```

### 6. 安全建议

1. **授权码安全**：
   - 不要将授权码分享给他人
   - 定期更换授权码
   - 如果泄露，立即重新生成

2. **邮箱安全**：
   - 使用专门的邮箱账号用于系统通知
   - 定期检查邮箱登录记录

3. **收件人设置**：
   - 可以设置多个收件人（用分号分隔）
   - 建议设置备用邮箱

---

## 📞 技术支持

如果遇到配置问题，请检查：
1. QQ邮箱SMTP服务是否正确开启
2. 授权码是否正确获取和填写
3. 网络连接是否正常
4. 防火墙是否阻止了SMTP连接

配置成功后，系统会自动发送告警邮件，帮助您及时了解网络设备状态！
