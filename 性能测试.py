#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本 - 测试邮件候选筛选的性能
"""

import time
import sqlite3
from datetime import datetime

def test_email_candidate_performance():
    """测试邮件候选筛选的性能"""
    
    db_file = "zte_alarms.db"
    
    print("🔍 开始性能测试...")
    print(f"📁 数据库文件: {db_file}")
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_file)
        cursor = conn.cursor()
        
        # 查询活跃告警数量
        cursor.execute("SELECT COUNT(*) FROM alarms WHERE is_active = 1")
        total_count = cursor.fetchone()[0]
        print(f"📊 活跃告警总数: {total_count}")
        
        # 测试查询性能
        start_time = time.time()
        
        cursor.execute("""
            SELECT 
                id, code_name, perceived_severity_name, me_name, ne_ip,
                alarm_raised_time, effective_duration_minutes, is_baseline,
                ack_state_name, alarm_type_name, reason_name, additional_text,
                position_name, alarm_code, res_type_name, clear_state_name,
                ack_user_id, comment_text, first_seen_at, last_seen_at,
                alarm_key, is_new, is_active, status_changed_at, created_at,
                actual_duration_minutes, raw_data
            FROM alarms 
            WHERE is_active = 1 
            ORDER BY alarm_raised_time DESC
        """)
        
        alarms = cursor.fetchall()
        query_time = time.time() - start_time
        print(f"⏱️ 数据库查询耗时: {query_time:.2f}秒")
        
        # 测试筛选逻辑性能
        start_time = time.time()
        
        # 模拟邮件候选筛选逻辑
        candidates = 0
        focus_keywords = ["CPU过载", "外部扩展设备故障", "时钟源异常"]
        
        for alarm in alarms:
            # 解析raw_data
            raw_data = {}
            try:
                import json
                if alarm[26]:  # raw_data字段
                    raw_data = json.loads(alarm[26])
            except:
                pass
            
            # 模拟筛选条件
            is_new_or_baseline = (alarm[21] == 1) or (alarm[7] == 1)  # is_new or is_baseline
            code_name = alarm[1] or ''  # code_name
            is_focus = any(kw in code_name for kw in focus_keywords)
            relationflag = raw_data.get('relationflag', 0)
            is_related = relationflag in (1, 2, 3)
            
            # 简化的运营商判断
            is_unicom = True  # 简化处理
            
            if is_new_or_baseline and is_unicom and (is_focus or is_related):
                # 简化的时间检查
                alarm_time = alarm[5]  # alarm_raised_time
                if alarm_time:
                    try:
                        if isinstance(alarm_time, str):
                            alarm_time = float(alarm_time)
                        if alarm_time > 1e12:
                            alarm_time = alarm_time / 1000.0
                        
                        start_dt = datetime.fromtimestamp(alarm_time)
                        time_diff = datetime.now() - start_dt
                        
                        # 如果超过6天，跳过
                        if time_diff.days > 6:
                            continue
                            
                        candidates += 1
                    except:
                        pass
        
        filter_time = time.time() - start_time
        print(f"⏱️ 候选筛选耗时: {filter_time:.2f}秒")
        print(f"📧 筛选出候选告警: {candidates}条")
        
        # 总体性能
        total_time = query_time + filter_time
        print(f"⏱️ 总耗时: {total_time:.2f}秒")
        
        # 性能评估
        if total_time < 1.0:
            print("✅ 性能优秀 (< 1秒)")
        elif total_time < 3.0:
            print("🟡 性能良好 (1-3秒)")
        elif total_time < 10.0:
            print("🟠 性能一般 (3-10秒)")
        else:
            print("🔴 性能较差 (> 10秒)")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_time_calculation_performance():
    """测试时间计算的性能"""
    print("\n🕐 测试时间计算性能...")
    
    # 模拟1000个时间戳
    timestamps = []
    base_time = time.time() - 86400 * 5  # 5天前
    for i in range(1000):
        timestamps.append(base_time + i * 60)  # 每分钟一个
    
    # 测试简单时间差计算
    start_time = time.time()
    for ts in timestamps:
        start_dt = datetime.fromtimestamp(ts)
        time_diff = datetime.now() - start_dt
        days = time_diff.days
    simple_time = time.time() - start_time
    
    print(f"⏱️ 简单时间差计算(1000次): {simple_time:.3f}秒")
    
    # 测试复杂考核时间计算
    start_time = time.time()
    for ts in timestamps[:100]:  # 只测试100次，因为太慢
        start_dt = datetime.fromtimestamp(ts)
        # 模拟复杂的考核时间计算
        from datetime import timedelta
        current_time = start_dt
        end_time = datetime.now()
        total_minutes = 0
        
        while current_time < end_time:
            day_start = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
            assessment_start = day_start.replace(hour=6)
            assessment_end = day_start.replace(hour=23, minute=59, second=59)
            next_day_start = day_start + timedelta(days=1)
            
            day_effective_start = max(current_time, assessment_start)
            day_effective_end = min(end_time, assessment_end)
            
            if day_effective_start <= day_effective_end and day_effective_start.hour >= 6:
                duration = day_effective_end - day_effective_start
                total_minutes += int(duration.total_seconds() / 60)
            
            current_time = next_day_start
    
    complex_time = time.time() - start_time
    print(f"⏱️ 复杂考核时间计算(100次): {complex_time:.3f}秒")
    print(f"📊 复杂计算比简单计算慢: {complex_time / (simple_time / 10):.1f}倍")

if __name__ == "__main__":
    print("🚀 告警监控系统 - 性能测试")
    print("=" * 50)
    
    test_email_candidate_performance()
    test_time_calculation_performance()
    
    print("\n" + "=" * 50)
    print("📝 性能优化建议:")
    print("1. 使用简单时间差判断替代复杂考核时间计算")
    print("2. 添加时间计算缓存，避免重复计算")
    print("3. 优先筛选条件，减少不必要的计算")
    print("4. 考虑异步处理大量数据")
    
    input("\n按回车键退出...")
