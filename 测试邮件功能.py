#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试邮件功能脚本
用于验证QQ邮箱配置是否正确
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from datetime import datetime

def test_qq_email():
    """测试QQ邮箱发送功能"""
    
    # QQ邮箱配置
    smtp_host = 'smtp.qq.com'
    smtp_port = 465
    use_ssl = True
    
    # 邮箱账号信息（需要用户填写）
    username = input("请输入QQ邮箱地址（例如：<EMAIL>）: ").strip()
    password = input("请输入QQ邮箱授权码（不是QQ密码）: ").strip()
    from_addr = username
    to_addr = input("请输入收件人邮箱地址: ").strip()
    
    # 验证输入
    if not all([username, password, to_addr]):
        print("❌ 请填写完整的邮箱信息")
        return False
    
    if '@' not in username or '@' not in to_addr:
        print("❌ 邮箱地址格式不正确")
        return False
    
    try:
        print(f"📧 开始测试邮件发送...")
        print(f"   SMTP服务器: {smtp_host}:{smtp_port}")
        print(f"   发件人: {from_addr}")
        print(f"   收件人: {to_addr}")
        print(f"   SSL: {'是' if use_ssl else '否'}")
        
        # 创建测试邮件
        msg = MIMEMultipart('alternative')
        msg['Subject'] = f"[测试邮件] 告警监控系统邮件测试 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        msg['From'] = from_addr
        msg['To'] = to_addr
        
        test_content = f"""
告警监控系统邮件测试

测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
发件人: {from_addr}
收件人: {to_addr}
SMTP服务器: {smtp_host}:{smtp_port}
SSL: {'是' if use_ssl else '否'}

如果您收到这封邮件，说明邮件配置正确！

请检查：
1. 收件箱
2. 垃圾邮件文件夹
3. 已删除文件夹
4. 邮件过滤规则

告警监控系统 - 邮件功能测试
        """
        msg.attach(MIMEText(test_content, 'plain', 'utf-8'))
        
        # 连接SMTP服务器
        print("🔗 连接SMTP服务器...")
        if use_ssl:
            server = smtplib.SMTP_SSL(smtp_host, smtp_port, timeout=15)
        else:
            server = smtplib.SMTP(smtp_host, smtp_port, timeout=15)
        
        # 登录
        print("🔐 进行SMTP认证...")
        server.login(username, password)
        
        # 发送邮件
        print("📤 发送邮件...")
        result = server.sendmail(from_addr, [to_addr], msg.as_string())
        
        # 关闭连接
        server.quit()
        
        if result:
            print(f"⚠️ 邮件发送完成但有警告: {result}")
        else:
            print("✅ 测试邮件发送成功！")
            print(f"📬 请检查收件箱: {to_addr}")
            print("💡 如果没有收到，请检查垃圾邮件文件夹")
        
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ SMTP认证失败: {e}")
        print("💡 请检查：")
        print("   1. 用户名是否为完整的QQ邮箱地址")
        print("   2. 密码是否为QQ邮箱的授权码（不是QQ密码）")
        print("   3. 是否已开启QQ邮箱的SMTP服务")
        return False
        
    except smtplib.SMTPRecipientsRefused as e:
        print(f"❌ 收件人被拒绝: {e}")
        print("💡 请检查收件人邮箱地址是否正确")
        return False
        
    except smtplib.SMTPSenderRefused as e:
        print(f"❌ 发件人被拒绝: {e}")
        print("💡 请检查发件人邮箱地址是否正确")
        return False
        
    except smtplib.SMTPConnectError as e:
        print(f"❌ SMTP连接错误: {e}")
        print("💡 请检查网络连接和防火墙设置")
        return False
        
    except Exception as e:
        print(f"❌ 邮件发送失败: {e}")
        return False

def show_qq_email_setup_guide():
    """显示QQ邮箱设置指南"""
    print("=" * 60)
    print("📧 QQ邮箱SMTP服务开启指南")
    print("=" * 60)
    print()
    print("1. 登录QQ邮箱 (https://mail.qq.com)")
    print("2. 点击右上角'设置' → '账户'")
    print("3. 找到'POP3/IMAP/SMTP/Exchange/CardDAV/CalDAV服务'")
    print("4. 开启'SMTP服务'")
    print("5. 按照提示发送短信，获取授权码（16位字符）")
    print("6. 保存好这个授权码，这就是邮件配置中的密码")
    print()
    print("⚠️ 重要提醒：")
    print("   • 密码必须使用授权码，不是QQ密码")
    print("   • 授权码是16位字符，类似：abcdefghijklmnop")
    print("   • 如果忘记授权码，可以重新生成")
    print()

if __name__ == "__main__":
    print("🚨 告警监控系统 - 邮件功能测试")
    print("=" * 50)
    
    # 显示设置指南
    show_setup = input("是否需要查看QQ邮箱设置指南？(y/n): ").strip().lower()
    if show_setup in ['y', 'yes', '是']:
        show_qq_email_setup_guide()
    
    # 开始测试
    print("\n开始邮件功能测试...")
    print("-" * 30)
    
    success = test_qq_email()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 邮件功能测试完成！")
        print("💡 如果测试成功，您可以在告警监控系统中使用相同的配置")
    else:
        print("❌ 邮件功能测试失败")
        print("💡 请根据错误提示检查配置，或查看设置指南")
    
    input("\n按回车键退出...")
