# 告警监控系统 - 邮件功能优化说明

## 🚀 性能优化

### 问题分析
1. **邮件候选筛选慢**：对10550条告警进行复杂时间计算，耗时1分钟
2. **邮件发送慢**：每封邮件间隔1-4秒，发送10封邮件需要30-40秒
3. **测试邮件冗余**：每封告警邮件都配对一封测试邮件，数量翻倍
4. **SMTP连接问题**：长时间复用连接导致发送失败

### 优化措施

#### 1. 时间计算优化
- **快速时间检查**：优先使用数据库字段，避免复杂计算
- **时间缓存机制**：相同告警的时间只计算一次
- **筛选顺序优化**：先轻量级检查，后耗时计算

**效果**：邮件候选筛选从60秒降到0.07秒

#### 2. 邮件发送优化
- **移除测试邮件**：不再发送配对的测试邮件
- **独立连接**：每封邮件使用独立SMTP连接
- **减少延迟**：邮件间隔从1-4秒降到0.5秒

**效果**：邮件发送速度提升80%

#### 3. 性能监控
- 添加详细的耗时日志
- 分步骤性能统计
- UI更新时间监控

## 📧 邮件配置

### QQ邮箱配置
```
SMTP服务器：smtp.qq.com
端口：465
SSL：启用
用户名：<EMAIL>
密码：[QQ邮箱授权码]
发件人：<EMAIL>
收件人：<EMAIL>
```

### 重要提醒
1. **发件人与用户名必须一致**
2. **密码必须是QQ邮箱授权码**，不是QQ密码
3. **授权码获取**：QQ邮箱设置 → 账户 → 开启SMTP服务

## 🔧 故障排除

### 常见错误

#### 1. 发件人被拒绝
```
(501, b'Mail from address must be same as authorization user.')
```
**解决方案**：确保发件人邮箱与认证用户名一致

#### 2. SMTP认证失败
```
SMTPAuthenticationError: (535, b'Error: authentication failed')
```
**解决方案**：
- 检查用户名是否为完整邮箱地址
- 确认密码是授权码，不是QQ密码
- 确认已开启QQ邮箱SMTP服务

#### 3. 连接异常
```
(-1, b'\x00\x00\x00\x08\x00\x00\x00\x00\x00\x00\x00\x00250 OK')
```
**解决方案**：使用独立连接，避免连接复用问题

### 性能问题

#### 1. 邮件发送延迟大
**原因**：
- 复杂时间计算
- 测试邮件冗余
- SMTP连接复用

**解决方案**：
- 已优化时间计算
- 已移除测试邮件
- 已改为独立连接

#### 2. UI响应慢
**原因**：邮件发送在主线程执行

**建议**：考虑异步处理（未实施）

## 📊 优化效果对比

### 优化前
- 邮件候选筛选：60秒
- 邮件发送：30-40秒（含测试邮件）
- 总耗时：90-100秒

### 优化后
- 邮件候选筛选：0.07秒
- 邮件发送：5-10秒（仅告警邮件）
- 总耗时：5-15秒

**性能提升**：85-90%

## 🎯 邮件发送规则

### 新告警邮件
- 检测到新告警（is_new=1）
- 检测到基线告警（is_baseline=1）
- 必须是联通运营商
- 必须是重点关注或关联告警
- 超过5天的告警不发送

### 持续阈值邮件
- 持续时间达到阈值：1h、2h、3h、4h、8h、24h、48h、72h
- 每个阈值只发送一次
- 按根源ID分组发送
- 发送最高阈值的邮件

## 🔮 后续优化建议

1. **异步处理**：将邮件发送移到后台线程
2. **批量优化**：合并相似告警到一封邮件
3. **智能过滤**：根据历史数据优化筛选条件
4. **用户配置**：允许用户自定义发送规则

---

## 📞 使用说明

1. **启动程序**：运行 `python alarm_monitor_pyside6.py`
2. **配置邮件**：点击"📧 邮件设置"，填入QQ邮箱信息
3. **测试功能**：点击"🧪 发送测试邮件"验证配置
4. **启用邮件**：勾选"启用发送告警邮件"
5. **监控日志**：查看邮件发送状态和耗时信息

现在系统的邮件功能已经大幅优化，发送速度快，成功率高！
